import 'dart:convert';
import 'package:get/get.dart';
import '../../controllers/api_config_controller.dart';
import '../../models/character_card.dart';
import '../../models/world_building.dart';
import '../../models/character_profile.dart';
import '../../models/novel.dart';
import '../models/novel_memory.dart';
import '../models/writing_style_package.dart';
import '../chains/novel_generation_chain.dart';
import '../adapters/model_adapter.dart';
import 'novel_generation_service.dart';

/// 增强的小说生成服务
/// 支持世界观构建和角色档案管理
class EnhancedNovelGenerationService extends GetxService {
  final ApiConfigController _apiConfigController = Get.find<ApiConfigController>();
  final NovelGenerationService _baseService = Get.find<NovelGenerationService>();

  /// 生成世界观
  Future<WorldBuilding> generateWorldBuilding({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int totalChapters,
    String? background,
    String? otherRequirements,
    Map<String, CharacterCard>? characterCards,
    void Function(String)? onProgress,
    String? sessionId,
  }) async {
    onProgress?.call('正在分析章节数量，确定世界观复杂度...');
    
    final complexity = WorldBuildingComplexity.fromChapterCount(totalChapters);
    
    onProgress?.call('开始生成${complexity.displayName}世界观...');
    
    // 构建世界观生成提示词
    final prompt = _buildWorldBuildingPrompt(
      novelTitle: novelTitle,
      genres: genres,
      theme: theme,
      targetReaders: targetReaders,
      totalChapters: totalChapters,
      complexity: complexity,
      background: background,
      otherRequirements: otherRequirements,
      characterCards: characterCards,
    );

    try {
      // 调用AI生成世界观
      final response = await _callLLMWithPrompt(prompt, novelTitle);
      
      onProgress?.call('正在解析世界观内容...');
      
      // 解析响应并创建WorldBuilding对象
      final worldBuilding = _parseWorldBuildingResponse(
        response: response,
        novelTitle: novelTitle,
        complexity: complexity,
      );

      // 保存到内存
      final novelMemory = NovelMemory(novelTitle: novelTitle, sessionId: sessionId);
      await novelMemory.saveEnhancedWorldBuilding(worldBuilding.getFormattedDescription());

      onProgress?.call('世界观生成完成！');
      
      return worldBuilding;
    } catch (e) {
      print('生成世界观时出错: $e');
      rethrow;
    }
  }

  /// 生成角色档案
  Future<Map<String, CharacterProfile>> generateCharacterProfiles({
    required WorldBuilding worldBuilding,
    required Map<String, CharacterCard> characterCards,
    void Function(String)? onProgress,
    String? sessionId,
  }) async {
    final profiles = <String, CharacterProfile>{};
    
    onProgress?.call('开始为${characterCards.length}个角色生成详细档案...');
    
    int current = 0;
    for (final entry in characterCards.entries) {
      current++;
      final characterId = entry.key;
      final characterCard = entry.value;
      
      onProgress?.call('正在生成${characterCard.name}的角色档案... ($current/${characterCards.length})');
      
      // 为每个角色生成档案
      final profile = await _generateSingleCharacterProfile(
        worldBuilding: worldBuilding,
        characterCard: characterCard,
        allCharacters: characterCards,
        sessionId: sessionId,
      );
      
      profiles[characterId] = profile;
    }

    // 保存到内存
    final novelMemory = NovelMemory(novelTitle: worldBuilding.novelId, sessionId: sessionId);
    final profilesJson = profiles.map((key, value) => MapEntry(key, value.toJson()));
    await novelMemory.saveCharacterProfiles(profilesJson);

    onProgress?.call('所有角色档案生成完成！');
    
    return profiles;
  }

  /// 生成增强大纲（包含世界观和角色档案）
  Future<String> generateEnhancedOutline({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int totalChapters,
    required WorldBuilding worldBuilding,
    required Map<String, CharacterProfile> characterProfiles,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
    void Function(String)? onProgress,
    String? sessionId,
  }) async {
    onProgress?.call('正在基于世界观和角色档案生成大纲...');
    
    // 构建增强大纲提示词
    final prompt = _buildEnhancedOutlinePrompt(
      novelTitle: novelTitle,
      genres: genres,
      theme: theme,
      targetReaders: targetReaders,
      totalChapters: totalChapters,
      worldBuilding: worldBuilding,
      characterProfiles: characterProfiles,
      background: background,
      otherRequirements: otherRequirements,
      writingStyle: writingStyle,
    );

    try {
      // 使用基础服务生成大纲
      final outline = await _baseService.generateOutline(
        novelTitle: novelTitle,
        genres: genres,
        theme: theme,
        targetReaders: targetReaders,
        totalChapters: totalChapters,
        background: background,
        otherRequirements: otherRequirements,
        writingStyle: writingStyle,
        characterCards: _convertProfilesToCards(characterProfiles),
        onProgress: (current, total) => onProgress?.call('生成大纲进度: $current/$total'),
      );

      onProgress?.call('增强大纲生成完成！');
      return outline;
    } catch (e) {
      print('生成增强大纲时出错: $e');
      rethrow;
    }
  }

  /// 生成章节并更新角色档案
  Future<String> generateChapterWithProfileUpdate({
    required String novelTitle,
    required int chapterNumber,
    required String chapterTitle,
    required String outlineContent,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required WorldBuilding worldBuilding,
    required Map<String, CharacterProfile> characterProfiles,
    String? background,
    String? otherRequirements,
    String? previousChapterSummary,
    WritingStylePackage? writingStyle,
    void Function(String)? onProgress,
    String? sessionId,
  }) async {
    onProgress?.call('正在生成第$chapterNumber章内容...');
    
    // 生成章节内容
    final chapterContent = await _baseService.generateChapter(
      novelTitle: novelTitle,
      chapterNumber: chapterNumber,
      chapterTitle: chapterTitle,
      outlineContent: outlineContent,
      genres: genres,
      theme: theme,
      targetReaders: targetReaders,
      background: background,
      otherRequirements: otherRequirements,
      previousChapterSummary: previousChapterSummary,
      writingStyle: writingStyle,
      characterCards: _convertProfilesToCards(characterProfiles),
      onProgress: onProgress,
      sessionId: sessionId,
    );

    onProgress?.call('章节内容生成完成，正在分析角色发展...');
    
    // 分析并更新角色档案
    final updatedProfiles = await _analyzeAndUpdateCharacterProfiles(
      chapterNumber: chapterNumber,
      chapterContent: chapterContent,
      characterProfiles: characterProfiles,
      onProgress: onProgress,
      sessionId: sessionId,
    );

    // 保存更新后的角色档案
    final novelMemory = NovelMemory(novelTitle: novelTitle, sessionId: sessionId);
    final profilesJson = updatedProfiles.map((key, value) => MapEntry(key, value.toJson()));
    await novelMemory.saveCharacterProfiles(profilesJson);

    onProgress?.call('第$chapterNumber章生成完成，角色档案已更新！');
    
    return chapterContent;
  }

  // ========== 私有辅助方法 ==========

  String _buildWorldBuildingPrompt({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int totalChapters,
    required WorldBuildingComplexity complexity,
    String? background,
    String? otherRequirements,
    Map<String, CharacterCard>? characterCards,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('# 世界观构建任务');
    buffer.writeln('请为小说《$novelTitle》构建${complexity.displayName}的世界观设定。');
    buffer.writeln();
    buffer.writeln('## 基本信息');
    buffer.writeln('- 小说标题：$novelTitle');
    buffer.writeln('- 类型：${genres.join(', ')}');
    buffer.writeln('- 主题：$theme');
    buffer.writeln('- 目标读者：$targetReaders');
    buffer.writeln('- 总章节数：$totalChapters');
    if (background?.isNotEmpty == true) {
      buffer.writeln('- 背景设定：$background');
    }
    if (otherRequirements?.isNotEmpty == true) {
      buffer.writeln('- 其他要求：$otherRequirements');
    }
    buffer.writeln();

    // 根据复杂度调整要求
    buffer.writeln('## 世界观要求');
    buffer.writeln('根据${totalChapters}章的篇幅，请构建${complexity.displayName}的世界观：');
    
    switch (complexity) {
      case WorldBuildingComplexity.simple:
        buffer.writeln('- 重点突出核心设定，避免过于复杂');
        buffer.writeln('- 时代背景：简洁明了的时代设定');
        buffer.writeln('- 地理环境：主要活动场所的基本描述');
        buffer.writeln('- 社会结构：基本的社会关系和等级');
        buffer.writeln('- 文化背景：影响故事的主要文化元素');
        break;
      case WorldBuildingComplexity.moderate:
        buffer.writeln('- 平衡设定复杂度与故事需求');
        buffer.writeln('- 时代背景：详细的历史背景和时代特征');
        buffer.writeln('- 地理环境：多个重要地点的详细描述');
        buffer.writeln('- 社会结构：完整的社会体系和权力结构');
        buffer.writeln('- 文化背景：丰富的文化传统和价值观念');
        buffer.writeln('- 力量体系：如有必要，描述特殊能力或技能体系');
        break;
      case WorldBuildingComplexity.complex:
        buffer.writeln('- 构建完整详细的世界体系');
        buffer.writeln('- 时代背景：深入的历史脉络和时代变迁');
        buffer.writeln('- 地理环境：完整的世界地图和地理体系');
        buffer.writeln('- 社会结构：复杂的政治、经济、社会体系');
        buffer.writeln('- 文化背景：多元化的文化体系和价值观冲突');
        buffer.writeln('- 力量体系：详细的能力体系和成长规则');
        buffer.writeln('- 特殊元素：独特的物品、地点、历史事件');
        break;
    }

    if (characterCards?.isNotEmpty == true) {
      buffer.writeln();
      buffer.writeln('## 角色信息');
      buffer.writeln('请确保世界观与以下角色设定相匹配：');
      for (final card in characterCards!.values) {
        buffer.writeln('- ${card.name}：${card.personalityTraits}');
      }
    }

    buffer.writeln();
    buffer.writeln('请以结构化的方式输出世界观设定，确保内容紧凑且符合${totalChapters}章的故事需求。');

    return buffer.toString();
  }

  WorldBuilding _parseWorldBuildingResponse({
    required String response,
    required String novelTitle,
    required WorldBuildingComplexity complexity,
  }) {
    // 这里应该实现更智能的解析逻辑
    // 暂时使用简单的文本分割方式
    
    return WorldBuilding(
      novelId: novelTitle,
      timeBackground: _extractSection(response, '时代背景') ?? '现代都市',
      geographicalSetting: _extractSection(response, '地理环境') ?? '城市环境',
      socialStructure: _extractSection(response, '社会结构') ?? '现代社会',
      culturalBackground: _extractSection(response, '文化背景') ?? '现代文化',
      powerSystem: _extractSection(response, '力量体系'),
      economicSystem: _extractSection(response, '经济体系'),
      politicalSystem: _extractSection(response, '政治体系'),
      technologyLevel: _extractSection(response, '科技水平'),
      specialItems: _extractSection(response, '特殊物品'),
      importantLocations: _extractSection(response, '重要地点'),
      historicalEvents: _extractSection(response, '历史事件'),
      myths: _extractSection(response, '神话传说'),
      complexity: complexity,
    );
  }

  String? _extractSection(String text, String sectionName) {
    final pattern = RegExp('$sectionName[：:](.*?)(?=\\n[\\*\\-]|\\n\\n|\$)', dotAll: true);
    final match = pattern.firstMatch(text);
    return match?.group(1)?.trim();
  }

  Future<CharacterProfile> _generateSingleCharacterProfile({
    required WorldBuilding worldBuilding,
    required CharacterCard characterCard,
    required Map<String, CharacterCard> allCharacters,
    String? sessionId,
  }) async {
    // 构建角色档案生成提示词
    final prompt = _buildCharacterProfilePrompt(
      worldBuilding: worldBuilding,
      characterCard: characterCard,
      allCharacters: allCharacters,
    );

    try {
      final response = await _callLLMWithPrompt(prompt, worldBuilding.novelId);

      // 创建角色档案
      return CharacterProfile(
        novelId: worldBuilding.novelId,
        characterId: characterCard.id,
        baseInfo: characterCard,
        relationships: _parseRelationships(response, allCharacters),
      );
    } catch (e) {
      print('生成角色档案时出错: $e');
      // 返回基础档案
      return CharacterProfile(
        novelId: worldBuilding.novelId,
        characterId: characterCard.id,
        baseInfo: characterCard,
      );
    }
  }

  String _buildCharacterProfilePrompt({
    required WorldBuilding worldBuilding,
    required CharacterCard characterCard,
    required Map<String, CharacterCard> allCharacters,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('# 角色档案生成任务');
    buffer.writeln('请为角色${characterCard.name}生成详细档案，确保与世界观设定相符。');
    buffer.writeln();
    buffer.writeln('## 世界观背景');
    buffer.writeln(worldBuilding.getFormattedDescription());
    buffer.writeln();
    buffer.writeln('## 角色基础信息');
    buffer.writeln('- 姓名：${characterCard.name}');
    buffer.writeln('- 性格：${characterCard.personalityTraits}');
    buffer.writeln('- 背景：${characterCard.background}');
    buffer.writeln();
    
    if (allCharacters.length > 1) {
      buffer.writeln('## 其他角色');
      for (final other in allCharacters.values) {
        if (other.id != characterCard.id) {
          buffer.writeln('- ${other.name}：${other.personalityTraits}');
        }
      }
      buffer.writeln();
    }
    
    buffer.writeln('请分析该角色在世界观中的定位，以及与其他角色的关系。');
    buffer.writeln('重点关注：');
    buffer.writeln('1. 角色在世界观中的具体位置和作用');
    buffer.writeln('2. 与其他角色的关系网络');
    buffer.writeln('3. 角色的成长潜力和发展方向');

    return buffer.toString();
  }

  Map<String, String> _parseRelationships(String response, Map<String, CharacterCard> allCharacters) {
    final relationships = <String, String>{};
    
    // 简单的关系解析逻辑
    for (final character in allCharacters.values) {
      if (response.contains(character.name)) {
        // 提取关系描述
        final pattern = RegExp('${character.name}[：:]([^\\n]+)');
        final match = pattern.firstMatch(response);
        if (match != null) {
          relationships[character.id] = match.group(1)?.trim() ?? '相识';
        }
      }
    }
    
    return relationships;
  }

  String _buildEnhancedOutlinePrompt({
    required String novelTitle,
    required List<String> genres,
    required String theme,
    required String targetReaders,
    required int totalChapters,
    required WorldBuilding worldBuilding,
    required Map<String, CharacterProfile> characterProfiles,
    String? background,
    String? otherRequirements,
    WritingStylePackage? writingStyle,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('# 增强大纲生成任务');
    buffer.writeln('基于详细的世界观和角色档案，为小说《$novelTitle》生成$totalChapters章的详细大纲。');
    buffer.writeln();
    buffer.writeln('## 世界观设定');
    buffer.writeln(worldBuilding.getFormattedDescription());
    buffer.writeln();
    buffer.writeln('## 角色档案');
    for (final profile in characterProfiles.values) {
      buffer.writeln(profile.getFormattedProfile());
      buffer.writeln('---');
    }
    buffer.writeln();
    buffer.writeln('请确保大纲充分利用世界观设定和角色关系，创造紧凑有趣的情节发展。');

    return buffer.toString();
  }

  Map<String, CharacterCard> _convertProfilesToCards(Map<String, CharacterProfile> profiles) {
    return profiles.map((key, profile) => MapEntry(key, profile.baseInfo));
  }

  Future<Map<String, CharacterProfile>> _analyzeAndUpdateCharacterProfiles({
    required int chapterNumber,
    required String chapterContent,
    required Map<String, CharacterProfile> characterProfiles,
    void Function(String)? onProgress,
    String? sessionId,
  }) async {
    final updatedProfiles = <String, CharacterProfile>{};
    
    for (final entry in characterProfiles.entries) {
      final characterId = entry.key;
      final profile = entry.value;
      
      onProgress?.call('正在分析${profile.baseInfo.name}在第$chapterNumber章的发展...');
      
      // 分析角色在本章的发展
      final development = await _analyzeCharacterDevelopment(
        chapterNumber: chapterNumber,
        chapterContent: chapterContent,
        characterProfile: profile,
      );
      
      if (development.isNotEmpty) {
        onProgress?.call('${profile.baseInfo.name}的档案已更新');
        updatedProfiles[characterId] = profile.addDevelopment(chapterNumber, development);
      } else {
        updatedProfiles[characterId] = profile;
      }
    }
    
    return updatedProfiles;
  }

  Future<String> _analyzeCharacterDevelopment({
    required int chapterNumber,
    required String chapterContent,
    required CharacterProfile characterProfile,
  }) async {
    // 构建角色发展分析提示词
    final prompt = '''
# 角色发展分析任务

请分析角色${characterProfile.baseInfo.name}在第$chapterNumber章中的发展变化。

## 角色基础信息
${characterProfile.getFormattedProfile()}

## 第$chapterNumber章内容
$chapterContent

请用2-3句话概括该角色在本章中的主要发展，包括：
1. 情感或心理状态的变化
2. 与其他角色关系的发展
3. 能力或认知的提升
4. 重要的行为或决定

如果角色在本章中没有明显发展，请回复"无明显发展"。
''';

    try {
      final response = await _callLLMWithPrompt(prompt, characterProfile.novelId);

      // 如果AI回复表示无发展，返回空字符串
      if (response.contains('无明显发展') || response.trim().isEmpty) {
        return '';
      }
      
      return response.trim();
    } catch (e) {
      print('分析角色发展时出错: $e');
      return '';
    }
  }

  /// 调用LLM的辅助方法
  Future<String> _callLLMWithPrompt(String prompt, String novelTitle) async {
    try {
      // 获取当前模型配置
      final modelConfig = _apiConfigController.getCurrentModel();

      // 创建临时链来调用LLM
      final llm = ModelAdapter.createLLMFromConfig(modelConfig);
      final tempChain = NovelGenerationChain(llm: llm, novelTitle: novelTitle);

      // 构建输入
      final input = {
        'input': prompt,
        'task': 'custom_generation',
      };

      // 调用LLM
      final response = await tempChain.run(input);
      return response;
    } catch (e) {
      print('调用LLM失败: $e');
      // 返回默认响应以避免完全失败
      return _getDefaultResponse();
    }
  }

  String _getDefaultResponse() {
    return '''
时代背景：现代都市，21世纪初期
地理环境：繁华的国际大都市，高楼林立
社会结构：现代商业社会，阶层分明
文化背景：多元文化融合的现代都市文化
力量体系：商业竞争和人际关系网络
''';
  }
}
