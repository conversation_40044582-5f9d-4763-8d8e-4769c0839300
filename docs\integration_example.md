# 增强小说功能集成示例

## 📱 路由配置

### 在main.dart中添加路由

```dart
// 在GetMaterialApp的getPages中添加
GetPage(
  name: '/enhanced-novel',
  page: () => const EnhancedNovelScreen(),
),
```

### 在主界面添加入口

```dart
// 在主界面的功能列表中添加
Card(
  child: ListTile(
    leading: const Icon(Icons.auto_awesome, color: Colors.purple),
    title: const Text('增强小说生成'),
    subtitle: const Text('智能世界观 • 动态角色档案 • 情节连贯'),
    trailing: const Icon(Icons.arrow_forward_ios),
    onTap: () => Get.toNamed('/enhanced-novel'),
  ),
),
```

## 🔧 服务初始化检查

确保在main.dart中正确初始化了所有服务：

```dart
// 检查这些服务是否已注册
Get.put(EnhancedNovelGenerationService());
Get.put(EnhancedNovelController());
```

## 🎨 UI集成示例

### 在现有小说生成界面添加增强选项

```dart
// 在小说生成设置中添加开关
Obx(() => SwitchListTile(
  title: const Text('启用增强功能'),
  subtitle: const Text('智能世界观和动态角色档案'),
  value: _useEnhancedFeatures.value,
  onChanged: (value) => _useEnhancedFeatures.value = value,
)),

// 根据开关状态调用不同的生成方法
if (_useEnhancedFeatures.value) {
  // 使用增强功能
  await Get.find<EnhancedNovelController>().generateEnhancedNovel(...);
} else {
  // 使用原有功能
  await Get.find<NovelController>().generateNovel(...);
}
```

### 在小说详情页显示增强信息

```dart
// 检查小说是否使用了增强功能
if (novel.hasEnhancedFeatures) {
  ExpansionTile(
    title: const Text('增强信息'),
    children: [
      ListTile(
        title: const Text('世界观设定'),
        onTap: () => _showWorldBuilding(),
      ),
      ListTile(
        title: const Text('角色档案'),
        onTap: () => _showCharacterProfiles(),
      ),
    ],
  ),
}
```

## 📊 数据迁移处理

### 检查现有数据兼容性

```dart
// 在应用启动时检查数据版本
class DataMigrationService {
  static Future<void> checkAndMigrate() async {
    final box = await Hive.openBox('novels');
    
    for (final novel in box.values) {
      if (novel is Novel && novel.hasEnhancedFeatures == null) {
        // 为旧数据添加默认值
        final updatedNovel = novel.copyWith(hasEnhancedFeatures: false);
        await box.put(novel.id, updatedNovel);
      }
    }
  }
}
```

## 🔍 调试和测试

### 添加调试信息

```dart
// 在开发模式下显示详细信息
if (kDebugMode) {
  FloatingActionButton(
    onPressed: () {
      final controller = Get.find<EnhancedNovelController>();
      print('世界观: ${controller.currentWorldBuilding.value}');
      print('角色档案: ${controller.currentCharacterProfiles.length}');
    },
    child: const Icon(Icons.debug_info),
  ),
}
```

### 测试用例

```dart
// 简单的功能测试
void testEnhancedFeatures() async {
  final controller = Get.find<EnhancedNovelController>();
  
  // 测试世界观生成
  final worldBuilding = await controller.generateWorldBuilding(
    novelTitle: "测试小说",
    genres: ["测试"],
    theme: "测试主题",
    targetReaders: "测试读者",
    totalChapters: 10,
    characterCards: {},
  );
  
  assert(worldBuilding.complexity == WorldBuildingComplexity.simple);
  print('世界观生成测试通过');
}
```

## ⚡ 性能优化建议

### 懒加载角色档案

```dart
// 只在需要时加载角色档案详情
class CharacterProfileWidget extends StatelessWidget {
  final String characterId;
  
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<CharacterProfile?>(
      future: _loadCharacterProfile(characterId),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return CharacterProfileView(profile: snapshot.data!);
        }
        return const CircularProgressIndicator();
      },
    );
  }
}
```

### 缓存管理

```dart
// 定期清理缓存
class CacheManager {
  static Future<void> cleanupOldData() async {
    final controller = Get.find<EnhancedNovelController>();
    
    // 清理超过30天的缓存数据
    final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
    // 实现清理逻辑...
  }
}
```

## 🎯 用户体验优化

### 进度保存和恢复

```dart
// 保存生成进度
class GenerationProgressService {
  static Future<void> saveProgress({
    required String novelId,
    required int currentStep,
    required double progress,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('generation_progress_$novelId', jsonEncode({
      'currentStep': currentStep,
      'progress': progress,
      'timestamp': DateTime.now().toIso8601String(),
    }));
  }
  
  static Future<Map<String, dynamic>?> loadProgress(String novelId) async {
    final prefs = await SharedPreferences.getInstance();
    final progressJson = prefs.getString('generation_progress_$novelId');
    if (progressJson != null) {
      return jsonDecode(progressJson);
    }
    return null;
  }
}
```

### 错误恢复

```dart
// 生成失败时的恢复机制
try {
  await controller.generateEnhancedNovel(...);
} catch (e) {
  // 显示错误信息
  Get.snackbar('生成失败', '错误: $e');
  
  // 提供重试选项
  Get.dialog(
    AlertDialog(
      title: const Text('生成失败'),
      content: const Text('是否要重试？'),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: () {
            Get.back();
            _retryGeneration();
          },
          child: const Text('重试'),
        ),
      ],
    ),
  );
}
```

## 📱 移动端适配

### 响应式布局

```dart
// 根据屏幕大小调整布局
class ResponsiveEnhancedNovelScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth > 600) {
      // 平板布局：左右分栏
      return Row(
        children: [
          Expanded(flex: 1, child: _buildInputForm()),
          Expanded(flex: 1, child: _buildResultsPanel()),
        ],
      );
    } else {
      // 手机布局：上下排列
      return Column(
        children: [
          _buildInputForm(),
          _buildResultsPanel(),
        ],
      );
    }
  }
}
```

### 内存管理

```dart
// 在页面销毁时清理资源
class EnhancedNovelScreen extends StatefulWidget {
  @override
  void dispose() {
    // 清理控制器状态
    Get.find<EnhancedNovelController>().reset();
    super.dispose();
  }
}
```

通过以上集成示例，你可以将增强小说功能无缝集成到现有应用中，为用户提供更好的小说创作体验！
