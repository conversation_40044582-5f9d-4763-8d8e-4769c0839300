import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'character_card.dart';

part 'character_profile.g.dart';

/// 增强的角色档案模型
@HiveType(typeId: 17)
class CharacterProfile {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String novelId;

  @HiveField(2)
  final String characterId;

  @HiveField(3)
  final CharacterCard baseInfo; // 基础角色信息

  @HiveField(4)
  final List<CharacterDevelopment> developments; // 发展记录

  @HiveField(5)
  final Map<String, String> relationships; // 关系网络（简化版）

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  final DateTime updatedAt;

  CharacterProfile({
    String? id,
    required this.novelId,
    required this.characterId,
    required this.baseInfo,
    List<CharacterDevelopment>? developments,
    Map<String, String>? relationships,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       developments = developments ?? [],
       relationships = relationships ?? {},
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// 添加章节发展记录
  CharacterProfile addDevelopment(int chapterNumber, String development) {
    final newDevelopment = CharacterDevelopment(
      chapterNumber: chapterNumber,
      development: development,
      timestamp: DateTime.now(),
    );
    
    final updatedDevelopments = List<CharacterDevelopment>.from(developments);
    updatedDevelopments.add(newDevelopment);
    
    return copyWith(
      developments: updatedDevelopments,
      updatedAt: DateTime.now(),
    );
  }

  /// 更新关系
  CharacterProfile updateRelationship(String targetCharacterId, String relationship) {
    final updatedRelationships = Map<String, String>.from(relationships);
    updatedRelationships[targetCharacterId] = relationship;
    
    return copyWith(
      relationships: updatedRelationships,
      updatedAt: DateTime.now(),
    );
  }

  /// 获取格式化的档案描述
  String getFormattedProfile() {
    final buffer = StringBuffer();
    
    buffer.writeln('# ${baseInfo.name} 角色档案\n');
    
    // 基础信息
    buffer.writeln('## 基础信息');
    buffer.writeln('**姓名**: ${baseInfo.name}');
    buffer.writeln('**性别**: ${baseInfo.gender}');
    buffer.writeln('**年龄**: ${baseInfo.age}');
    if (baseInfo.race?.isNotEmpty == true) {
      buffer.writeln('**种族**: ${baseInfo.race}');
    }
    buffer.writeln('**外貌**: ${baseInfo.appearance}');
    buffer.writeln('**性格**: ${baseInfo.personalityTraits}');
    buffer.writeln('**背景**: ${baseInfo.background}\n');
    
    // 关系网络
    if (relationships.isNotEmpty) {
      buffer.writeln('## 人际关系');
      relationships.forEach((characterId, relationship) {
        buffer.writeln('- $relationship');
      });
      buffer.writeln();
    }
    
    // 发展历程
    if (developments.isNotEmpty) {
      buffer.writeln('## 发展历程');
      for (final dev in developments) {
        buffer.writeln('**第${dev.chapterNumber}章**: ${dev.development}');
      }
    }
    
    return buffer.toString();
  }

  /// 获取最近的发展记录（用于生成时的上下文）
  String getRecentDevelopments({int maxChapters = 3}) {
    if (developments.isEmpty) return '';
    
    final recentDevelopments = developments
        .where((dev) => developments.length - developments.indexOf(dev) <= maxChapters)
        .toList();
    
    if (recentDevelopments.isEmpty) return '';
    
    final buffer = StringBuffer();
    buffer.writeln('${baseInfo.name}近期发展：');
    for (final dev in recentDevelopments) {
      buffer.writeln('- 第${dev.chapterNumber}章：${dev.development}');
    }
    
    return buffer.toString();
  }

  CharacterProfile copyWith({
    String? novelId,
    String? characterId,
    CharacterCard? baseInfo,
    List<CharacterDevelopment>? developments,
    Map<String, String>? relationships,
    DateTime? updatedAt,
  }) {
    return CharacterProfile(
      id: id,
      novelId: novelId ?? this.novelId,
      characterId: characterId ?? this.characterId,
      baseInfo: baseInfo ?? this.baseInfo,
      developments: developments ?? this.developments,
      relationships: relationships ?? this.relationships,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'novelId': novelId,
    'characterId': characterId,
    'baseInfo': baseInfo.toJson(),
    'developments': developments.map((d) => d.toJson()).toList(),
    'relationships': relationships,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
  };

  factory CharacterProfile.fromJson(Map<String, dynamic> json) => CharacterProfile(
    id: json['id'] as String,
    novelId: json['novelId'] as String,
    characterId: json['characterId'] as String,
    baseInfo: CharacterCard.fromJson(json['baseInfo'] as Map<String, dynamic>),
    developments: (json['developments'] as List)
        .map((d) => CharacterDevelopment.fromJson(d as Map<String, dynamic>))
        .toList(),
    relationships: Map<String, String>.from(json['relationships'] as Map),
    createdAt: DateTime.parse(json['createdAt'] as String),
    updatedAt: DateTime.parse(json['updatedAt'] as String),
  );
}

/// 角色发展记录
@HiveType(typeId: 18)
class CharacterDevelopment {
  @HiveField(0)
  final int chapterNumber;

  @HiveField(1)
  final String development; // 2-3句话的发展描述

  @HiveField(2)
  final DateTime timestamp;

  CharacterDevelopment({
    required this.chapterNumber,
    required this.development,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() => {
    'chapterNumber': chapterNumber,
    'development': development,
    'timestamp': timestamp.toIso8601String(),
  };

  factory CharacterDevelopment.fromJson(Map<String, dynamic> json) => CharacterDevelopment(
    chapterNumber: json['chapterNumber'] as int,
    development: json['development'] as String,
    timestamp: DateTime.parse(json['timestamp'] as String),
  );
}
