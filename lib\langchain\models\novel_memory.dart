import 'dart:async';
import 'dart:collection';
import 'package:hive/hive.dart';

/// 用于管理和存储小说上下文信息的内存模型
class NovelMemory {
  final String novelTitle;
  final String? sessionId;

  // 内存缓存
  String? _outline;
  final HashMap<int, String> _chapters = HashMap<int, String>();
  final Map<int, String> _chapterTitles = {};

  String? _continuation;

  // 短篇小说专用缓存
  String? _shortNovelWorldBuilding;
  String? _shortNovelDetailedOutline;

  // 增强功能缓存
  String? _enhancedWorldBuilding; // 详细世界观
  Map<String, dynamic>? _characterProfiles; // 角色档案

  // Hive存储键前缀
  static const String _outlinePrefix = 'langchain_outline_';
  static const String _chapterPrefix = 'langchain_chapter_';

  static const String _continuationPrefix = 'langchain_continuation_';
  static const String _sessionPrefix = 'session_';

  // 短篇小说专用键前缀
  static const String _shortNovelWorldBuildingPrefix = 'langchain_short_world_';
  static const String _shortNovelDetailedOutlinePrefix = 'langchain_short_outline_';

  // 增强功能键前缀
  static const String _enhancedWorldBuildingPrefix = 'langchain_enhanced_world_';
  static const String _characterProfilesPrefix = 'langchain_character_profiles_';

  NovelMemory({required this.novelTitle, this.sessionId});

  // 为了避免频繁读写Hive，使用Completer进行异步读写操作的去重和合并
  final Map<String, Completer<dynamic>> _pendingOperations = {};

  /// 执行异步操作并确保相同键的操作不会重复执行
  Future<T> _executeOperation<T>(
      String key, Future<T> Function() operation) async {
    // 如果已有相同键的操作正在进行，等待其完成
    if (_pendingOperations.containsKey(key)) {
      return await _pendingOperations[key]!.future as T;
    }

    // 创建新的Completer并存储
    final completer = Completer<T>();
    _pendingOperations[key] = completer;

    try {
      // 执行操作
      final result = await operation();
      completer.complete(result);
      return result;
    } catch (e) {
      completer.completeError(e);
      rethrow;
    } finally {
      // 操作完成后移除Completer
      _pendingOperations.remove(key);
    }
  }

  /// 获取Box实例
  Future<Box> _getBox() async {
    const boxName = 'langchain_memory';
    if (Hive.isBoxOpen(boxName)) {
      return Hive.box(boxName);
    } else {
      return await Hive.openBox(boxName);
    }
  }

  /// 生成键名，如果有会话ID则使用会话ID作为前缀
  String _getOutlineKey() => sessionId != null
      ? '$_outlinePrefix$_sessionPrefix${sessionId}_$novelTitle'
      : '$_outlinePrefix$novelTitle';

  String _getChapterKey(int chapterNumber) => sessionId != null
      ? '$_chapterPrefix$_sessionPrefix${sessionId}_${novelTitle}_$chapterNumber'
      : '$_chapterPrefix${novelTitle}_$chapterNumber';

  String _getContinuationKey() => sessionId != null
      ? '$_continuationPrefix$_sessionPrefix${sessionId}_$novelTitle'
      : '$_continuationPrefix$novelTitle';

  String _getShortNovelWorldBuildingKey() => sessionId != null
      ? '$_shortNovelWorldBuildingPrefix$_sessionPrefix${sessionId}_$novelTitle'
      : '$_shortNovelWorldBuildingPrefix$novelTitle';

  String _getShortNovelDetailedOutlineKey() => sessionId != null
      ? '$_shortNovelDetailedOutlinePrefix$_sessionPrefix${sessionId}_$novelTitle'
      : '$_shortNovelDetailedOutlinePrefix$novelTitle';

  /// 保存大纲
  Future<void> saveOutline(String outline) async {
    // 立即更新内存缓存
    _outline = outline;

    // 异步写入Hive
    await _executeOperation<void>(_getOutlineKey(), () async {
      final box = await _getBox();
      await box.put(_getOutlineKey(), outline);
    });
  }

  /// 获取大纲
  Future<String?> getOutline() async {
    // 如果内存中有，直接返回
    if (_outline != null) {
      return _outline;
    }

    // 从Hive读取
    return await _executeOperation<String?>(_getOutlineKey(), () async {
      final box = await _getBox();
      final outline = box.get(_getOutlineKey());
      _outline = outline as String?;
      return _outline;
    });
  }

  /// 保存章节
  Future<void> saveChapter(
      int chapterNumber, String chapterTitle, String content) async {
    // 立即更新内存缓存
    _chapters[chapterNumber] = content;
    _chapterTitles[chapterNumber] = chapterTitle;

    // 异步写入Hive
    await _executeOperation<void>(_getChapterKey(chapterNumber), () async {
      final box = await _getBox();
      await box.put(_getChapterKey(chapterNumber), {
        'content': content,
        'title': chapterTitle,
      });
    });
  }

  /// 获取单个章节
  Future<String?> getChapter(int chapterNumber) async {
    // 如果内存中有，直接返回
    if (_chapters.containsKey(chapterNumber)) {
      return _chapters[chapterNumber];
    }

    // 从Hive读取
    return await _executeOperation<String?>(_getChapterKey(chapterNumber),
        () async {
      final box = await _getBox();
      final chapter = box.get(_getChapterKey(chapterNumber));
      if (chapter != null && chapter is Map) {
        final content = chapter['content'] as String?;
        final title = chapter['title'] as String?;

        if (content != null) {
          _chapters[chapterNumber] = content;
        }

        if (title != null) {
          _chapterTitles[chapterNumber] = title;
        }

        return content;
      }
      return null;
    });
  }

  /// 获取章节标题
  Future<String?> getChapterTitle(int chapterNumber) async {
    // 如果内存中有，直接返回
    if (_chapterTitles.containsKey(chapterNumber)) {
      return _chapterTitles[chapterNumber];
    }

    // 从Hive读取
    return await _executeOperation<String?>(_getChapterKey(chapterNumber),
        () async {
      final box = await _getBox();
      final chapter = box.get(_getChapterKey(chapterNumber));
      if (chapter != null && chapter is Map) {
        final title = chapter['title'] as String?;

        if (title != null) {
          _chapterTitles[chapterNumber] = title;
        }

        return title;
      }
      return null;
    });
  }

  /// 获取之前的所有章节内容，用于上下文
  /// 不再限制只获取前3章，而是获取所有之前的章节
  Future<String> getPreviousChapters(int currentChapter) async {
    final buffer = StringBuffer();

    print("[NovelMemory] 获取第 $currentChapter 章之前的所有章节内容");

    // 获取所有之前的章节
    for (int i = 1; i < currentChapter; i++) {
      final title = await getChapterTitle(i);
      final content = await getChapter(i);

      if (title != null && content != null) {
        buffer.writeln('第$i章：$title');
        buffer.writeln(content);
        buffer.writeln('\n---\n');
        print("[NovelMemory] 已添加第 $i 章内容，标题: $title");
      } else {
        print("[NovelMemory] 警告: 第 $i 章内容或标题为空");
      }
    }

    final result = buffer.toString();
    print("[NovelMemory] 已获取全部历史章节内容，总长度: ${result.length}");
    return result;
  }

  /// 获取全部小说内容，包括大纲和所有章节
  Future<String> getAllNovelContent() async {
    final buffer = StringBuffer();

    // 获取大纲
    final outline = await getOutline();
    if (outline != null && outline.isNotEmpty) {
      buffer.writeln('# 小说大纲');
      buffer.writeln(outline);
      buffer.writeln('\n---\n');
    }

    // 获取所有已生成的章节
    for (int i = 1; i <= 100; i++) {
      // 假设最多100章
      final title = await getChapterTitle(i);
      final content = await getChapter(i);

      if (title == null || content == null) {
        // 如果找不到章节，则认为已经到达结尾
        break;
      }

      buffer.writeln('第$i章：$title');
      buffer.writeln(content);
      buffer.writeln('\n---\n');
    }

    return buffer.toString();
  }

  /// 获取所有已生成的章节内容，返回章节号和内容的映射
  Future<Map<int, String>> getAllChapters() async {
    final Map<int, String> result = {};
    final box = await _getBox();

    // 首先检查内存缓存
    if (_chapters.isNotEmpty) {
      result.addAll(_chapters);
    }

    // 然后从 Hive 中获取所有章节
    // 假设最多100章
    for (int i = 1; i <= 100; i++) {
      // 如果内存中已有，跳过
      if (result.containsKey(i)) continue;

      final chapterKey = _getChapterKey(i);
      final chapter = box.get(chapterKey);

      if (chapter != null && chapter is Map) {
        final content = chapter['content'] as String?;
        if (content != null) {
          result[i] = content;
          // 更新内存缓存
          _chapters[i] = content;
        }
      } else {
        // 如果连续两个章节都不存在，则认为已经到达结尾
        if (!result.containsKey(i - 1)) {
          break;
        }
      }
    }

    print("[NovelMemory] 已获取全部章节，总数: ${result.length}");
    return result;
  }

  /// 保存续写内容
  Future<void> saveContinuation(String content) async {
    // 立即更新内存缓存
    _continuation = content;

    // 异步写入Hive
    await _executeOperation<void>(_getContinuationKey(), () async {
      final box = await _getBox();
      await box.put(_getContinuationKey(), content);
    });
  }

  /// 获取续写内容
  Future<String?> getContinuation() async {
    // 如果内存中有，直接返回
    if (_continuation != null) {
      return _continuation;
    }

    // 从Hive读取
    return await _executeOperation<String?>(_getContinuationKey(), () async {
      final box = await _getBox();
      final content = box.get(_getContinuationKey());
      _continuation = content as String?;
      return _continuation;
    });
  }

  /// 保存短篇小说世界观
  Future<void> saveShortNovelWorldBuilding(String worldBuilding) async {
    // 立即更新内存缓存
    _shortNovelWorldBuilding = worldBuilding;

    // 异步写入Hive
    await _executeOperation<void>(_getShortNovelWorldBuildingKey(), () async {
      final box = await _getBox();
      await box.put(_getShortNovelWorldBuildingKey(), worldBuilding);
    });
  }

  /// 获取短篇小说世界观
  Future<String?> getShortNovelWorldBuilding() async {
    // 如果内存中有，直接返回
    if (_shortNovelWorldBuilding != null) {
      return _shortNovelWorldBuilding;
    }

    // 从Hive读取
    return await _executeOperation<String?>(_getShortNovelWorldBuildingKey(), () async {
      final box = await _getBox();
      final content = box.get(_getShortNovelWorldBuildingKey());
      _shortNovelWorldBuilding = content as String?;
      return _shortNovelWorldBuilding;
    });
  }

  /// 保存短篇小说详细大纲
  Future<void> saveShortNovelDetailedOutline(String detailedOutline) async {
    // 立即更新内存缓存
    _shortNovelDetailedOutline = detailedOutline;

    // 异步写入Hive
    await _executeOperation<void>(_getShortNovelDetailedOutlineKey(), () async {
      final box = await _getBox();
      await box.put(_getShortNovelDetailedOutlineKey(), detailedOutline);
    });
  }

  /// 获取短篇小说详细大纲
  Future<String?> getShortNovelDetailedOutline() async {
    // 如果内存中有，直接返回
    if (_shortNovelDetailedOutline != null) {
      return _shortNovelDetailedOutline;
    }

    // 从Hive读取
    return await _executeOperation<String?>(_getShortNovelDetailedOutlineKey(), () async {
      final box = await _getBox();
      final content = box.get(_getShortNovelDetailedOutlineKey());
      _shortNovelDetailedOutline = content as String?;
      return _shortNovelDetailedOutline;
    });
  }

  /// 清除所有相关内存和持久化数据
  Future<void> clear() async {
    // 清除内存
    _outline = null;
    _chapters.clear();
    _chapterTitles.clear();

    _continuation = null;

    // 清除短篇小说专用缓存
    _shortNovelWorldBuilding = null;
    _shortNovelDetailedOutline = null;

    // 清除增强功能缓存
    _enhancedWorldBuilding = null;
    _characterProfiles = null;

    // 清除持久化数据
    await _executeOperation<void>('clear_memory_$novelTitle', () async {
      try {
        final box = await _getBox();

        // 如果有会话ID，只清除该会话的数据
        final String sessionPrefix =
            sessionId != null ? '$_sessionPrefix$sessionId' : '';

        // 根据会话ID筛选要删除的键
        final keysToDelete = box.keys.where((key) {
          if (!(key is String)) return false;

          // 如果有会话ID，只删除该会话的数据
          if (sessionId != null) {
            return (key.contains(sessionPrefix) && key.contains(novelTitle));
          } else {
            // 如果没有会话ID，只删除没有会话前缀的数据
            return (key.contains(novelTitle) &&
                !key.contains(_sessionPrefix) &&
                (key.startsWith(_outlinePrefix) ||
                    key.startsWith(_chapterPrefix) ||
                    key.startsWith(_continuationPrefix) ||
                    key.startsWith(_shortNovelWorldBuildingPrefix) ||
                    key.startsWith(_shortNovelDetailedOutlinePrefix) ||
                    key.startsWith(_enhancedWorldBuildingPrefix) ||
                    key.startsWith(_characterProfilesPrefix)));
          }
        }).toList(); // 使用 toList() 避免在迭代时修改集合

        if (keysToDelete.isNotEmpty) {
          print(
              'NovelMemory: Found ${keysToDelete.length} keys to delete in Hive box "${box.name}"');
          for (final key in keysToDelete) {
            await box.delete(key); // 使用 await 确保删除完成
          }
          print('NovelMemory: Deleted ${keysToDelete.length} keys from Hive.');
        } else {
          print(
              'NovelMemory: No keys found to delete in Hive box "${box.name}".');
        }
      } catch (e) {
        print('NovelMemory: Error clearing Hive data: $e');
        // 可以选择是否 rethrow
      }
    });
    print(
        'NovelMemory for "$novelTitle" clear operation requested.'); // 提示清除操作已请求
  }

  // ========== 增强功能方法 ==========

  /// 保存增强世界观
  Future<void> saveEnhancedWorldBuilding(String worldBuilding) async {
    // 立即更新内存缓存
    _enhancedWorldBuilding = worldBuilding;

    // 异步写入Hive
    await _executeOperation<void>(_getEnhancedWorldBuildingKey(), () async {
      final box = await _getBox();
      await box.put(_getEnhancedWorldBuildingKey(), worldBuilding);
    });
  }

  /// 获取增强世界观
  Future<String?> getEnhancedWorldBuilding() async {
    // 如果内存中有，直接返回
    if (_enhancedWorldBuilding != null) {
      return _enhancedWorldBuilding;
    }

    // 从Hive读取
    return await _executeOperation<String?>(_getEnhancedWorldBuildingKey(), () async {
      final box = await _getBox();
      final content = box.get(_getEnhancedWorldBuildingKey());
      _enhancedWorldBuilding = content as String?;
      return _enhancedWorldBuilding;
    });
  }

  /// 保存角色档案
  Future<void> saveCharacterProfiles(Map<String, dynamic> profiles) async {
    // 立即更新内存缓存
    _characterProfiles = profiles;

    // 异步写入Hive
    await _executeOperation<void>(_getCharacterProfilesKey(), () async {
      final box = await _getBox();
      await box.put(_getCharacterProfilesKey(), profiles);
    });
  }

  /// 获取角色档案
  Future<Map<String, dynamic>?> getCharacterProfiles() async {
    // 如果内存中有，直接返回
    if (_characterProfiles != null) {
      return _characterProfiles;
    }

    // 从Hive读取
    return await _executeOperation<Map<String, dynamic>?>(_getCharacterProfilesKey(), () async {
      final box = await _getBox();
      final content = box.get(_getCharacterProfilesKey());
      _characterProfiles = content as Map<String, dynamic>?;
      return _characterProfiles;
    });
  }

  /// 更新单个角色档案
  Future<void> updateCharacterProfile(String characterId, Map<String, dynamic> profile) async {
    final profiles = await getCharacterProfiles() ?? <String, dynamic>{};
    profiles[characterId] = profile;
    await saveCharacterProfiles(profiles);
  }

  // ========== 私有辅助方法 ==========

  String _getEnhancedWorldBuildingKey() {
    final sessionSuffix = sessionId != null ? '$_sessionPrefix$sessionId' : '';
    return '$_enhancedWorldBuildingPrefix${novelTitle.replaceAll(' ', '_')}$sessionSuffix';
  }

  String _getCharacterProfilesKey() {
    final sessionSuffix = sessionId != null ? '$_sessionPrefix$sessionId' : '';
    return '$_characterProfilesPrefix${novelTitle.replaceAll(' ', '_')}$sessionSuffix';
  }
}
