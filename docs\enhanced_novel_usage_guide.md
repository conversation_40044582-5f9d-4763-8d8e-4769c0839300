# 增强小说生成功能使用指南

## 🚀 快速开始

### 1. 访问增强功能
在应用主界面中，找到"增强小说生成"入口（需要在路由中添加）。

### 2. 基本信息填写
填写以下必要信息：
- **小说标题**：你的小说名称
- **类型**：用逗号分隔，如"都市,言情,现代"
- **主题**：小说的核心主题
- **目标读者**：如"年轻读者"、"都市白领"等
- **总章节数**：建议范围1-100章
- **背景设定**：可选，提供额外的背景信息

### 3. 章节数量与世界观复杂度对应关系

| 章节数量 | 世界观复杂度 | 包含内容 |
|---------|-------------|----------|
| 1-10章  | 简化 | 基础设定：时代背景、地理环境、社会结构、文化背景 |
| 11-30章 | 适中 | 基础设定 + 力量体系、经济体系、政治体系、科技水平 |
| 31+章   | 详细 | 全部设定 + 特殊物品、重要地点、历史事件、神话传说 |

## 📋 使用流程

### 方式一：一键生成（推荐新手）
1. 填写完整的基本信息
2. 确保已创建角色卡片
3. 点击"生成完整小说"按钮
4. 等待生成完成，观察进度提示

### 方式二：分步生成（推荐高级用户）
1. **第一步：生成世界观**
   - 点击"生成世界观"按钮
   - 等待世界观生成完成
   - 查看世界观摘要

2. **第二步：生成角色档案**
   - 确保世界观已生成
   - 点击"生成角色档案"按钮
   - 查看角色档案列表

3. **第三步：生成完整小说**
   - 确保前两步已完成
   - 点击"生成完整小说"按钮
   - 观察章节生成进度

## 🎯 功能特色

### 智能世界观生成
- **自动适配**：根据章节数量自动调整世界观详细程度
- **紧凑情节**：短篇重点突出核心设定，长篇构建完整世界体系
- **角色匹配**：确保世界观与角色设定相符

### 动态角色档案
- **初始档案**：基于世界观为每个角色生成详细档案
- **关系网络**：自动分析角色间的关系
- **动态更新**：每章生成后自动分析角色发展
- **简洁记录**：每章发展用2-3句话概括，避免冗余

### 情节连贯性保证
- **完整上下文**：每次生成新章节时传入世界观和角色档案
- **发展轨迹**：基于角色的历史发展生成合理的后续情节
- **一致性检查**：确保新内容与已有设定保持一致

## 📊 进度反馈说明

生成过程中会显示详细的进度信息：

```
正在分析章节数量，确定世界观复杂度... (5%)
开始生成适中世界观... (10%)
世界观生成完成！ (20%)
开始为3个角色生成详细档案... (25%)
正在生成张三的角色档案... (1/3) (30%)
正在生成李四的角色档案... (2/3) (35%)
所有角色档案生成完成！ (40%)
正在基于世界观和角色档案生成大纲... (50%)
增强大纲生成完成！ (60%)
正在生成第1章内容... (65%)
章节内容生成完成，正在分析角色发展... (70%)
张三的档案已更新 (75%)
第1章生成完成，角色档案已更新！ (80%)
...
增强小说生成完成！ (100%)
```

## 🔍 查看生成结果

### 世界观摘要
点击"世界观设定"展开项可以查看：
- 时代背景
- 地理环境  
- 社会结构
- 文化背景
- 其他详细设定（根据复杂度）

### 角色档案
点击"角色档案"展开项可以查看：
- 角色列表
- 基础信息
- 点击角色名称查看详细发展历程

### 角色发展历程示例
```
张三 发展历程：

第1章：初次觉醒异能，内心震惊但努力保持冷静。与李四的关系因为共同秘密而更加紧密。
第2章：在面对危机时展现出领导才能，开始承担更多责任。对自己的能力有了更深的认识。
第3章：经历挫折后变得更加成熟，学会了团队合作的重要性。与反派的对抗中展现出坚韧不拔的品质。
```

## ⚠️ 注意事项

### 角色卡片准备
- 在使用增强功能前，请先创建好角色卡片
- 角色信息越详细，生成的档案质量越高
- 建议至少包含主角、女主角等核心角色

### 生成时间
- 世界观生成：约1-2分钟
- 角色档案生成：每个角色约30秒-1分钟
- 章节生成：每章约2-3分钟
- 总时间取决于章节数量和角色数量

### 网络要求
- 需要稳定的网络连接
- 建议在WiFi环境下使用
- 生成过程中请勿关闭应用

### 存储空间
- 增强功能会生成更多数据
- 确保设备有足够的存储空间
- 定期清理不需要的小说数据

## 🛠️ 故障排除

### 常见问题

**Q: 生成失败怎么办？**
A: 检查网络连接，确认API配置正确，重试生成。

**Q: 角色档案没有更新？**
A: 确保角色在章节中有出现，系统只更新有发展的角色。

**Q: 世界观太简单/太复杂？**
A: 调整章节数量，系统会自动适配世界观复杂度。

**Q: 生成的内容不连贯？**
A: 确保使用增强功能完整流程，不要跳过世界观和角色档案生成。

### 性能优化建议
- 首次使用建议选择较少的章节数（10-20章）
- 角色数量建议控制在3-5个
- 定期清理缓存数据

## 📈 最佳实践

### 角色设定
- 为主要角色提供详细的背景故事
- 设定明确的性格特征和动机
- 考虑角色间的关系和冲突

### 世界观设计
- 提供清晰的背景设定信息
- 考虑故事的时代背景和地理环境
- 为特殊设定（如魔法、科技）提供说明

### 章节规划
- 短篇小说（1-10章）：重点突出核心冲突
- 中篇小说（11-30章）：平衡角色发展和情节推进
- 长篇小说（31+章）：构建完整的世界体系和角色成长弧

通过合理使用增强功能，你可以创作出更加连贯、丰富和引人入胜的小说作品！
