import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/enhanced_novel_controller.dart';
import '../../controllers/novel_controller.dart';
import '../../models/character_card.dart';

/// 增强小说生成界面
class EnhancedNovelScreen extends StatefulWidget {
  const EnhancedNovelScreen({Key? key}) : super(key: key);

  @override
  State<EnhancedNovelScreen> createState() => _EnhancedNovelScreenState();
}

class _EnhancedNovelScreenState extends State<EnhancedNovelScreen> {
  final EnhancedNovelController _enhancedController = Get.find<EnhancedNovelController>();
  final NovelController _novelController = Get.find<NovelController>();

  final _titleController = TextEditingController();
  final _genreController = TextEditingController();
  final _themeController = TextEditingController();
  final _targetReadersController = TextEditingController();
  final _chaptersController = TextEditingController(text: '20');
  final _backgroundController = TextEditingController();

  @override
  void dispose() {
    _titleController.dispose();
    _genreController.dispose();
    _themeController.dispose();
    _targetReadersController.dispose();
    _chaptersController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('增强小说生成'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoCard(),
                    const SizedBox(height: 16),
                    _buildInputForm(),
                    const SizedBox(height: 16),
                    _buildProgressSection(),
                    const SizedBox(height: 16),
                    _buildResultsSection(),
                  ],
                ),
              ),
            ),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.auto_awesome, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  '增强功能说明',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '• 根据章节数量自动调整世界观复杂度\n'
              '• 为每个角色生成详细档案并动态更新\n'
              '• 每章生成后自动分析角色发展\n'
              '• 确保情节连贯性和角色一致性',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '基本信息',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: '小说标题',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _genreController,
              decoration: const InputDecoration(
                labelText: '类型（用逗号分隔）',
                hintText: '例如：都市,言情,现代',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _themeController,
              decoration: const InputDecoration(
                labelText: '主题',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _targetReadersController,
              decoration: const InputDecoration(
                labelText: '目标读者',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _chaptersController,
              decoration: const InputDecoration(
                labelText: '总章节数',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _backgroundController,
              decoration: const InputDecoration(
                labelText: '背景设定（可选）',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection() {
    return Obx(() {
      if (!_enhancedController.isGenerating.value) {
        return const SizedBox.shrink();
      }

      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '生成进度',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              LinearProgressIndicator(
                value: _enhancedController.progress.value,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).primaryColor,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                _enhancedController.currentStep.value,
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),
              Text(
                '${(_enhancedController.progress.value * 100).toInt()}%',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildResultsSection() {
    return Obx(() {
      final worldBuilding = _enhancedController.currentWorldBuilding.value;
      final profiles = _enhancedController.currentCharacterProfiles;

      if (worldBuilding == null && profiles.isEmpty) {
        return const SizedBox.shrink();
      }

      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '生成结果',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              
              if (worldBuilding != null) ...[
                ExpansionTile(
                  title: const Text('世界观设定'),
                  leading: const Icon(Icons.public),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        _enhancedController.getWorldBuildingSummary(),
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ],
              
              if (profiles.isNotEmpty) ...[
                ExpansionTile(
                  title: Text('角色档案 (${profiles.length}个)'),
                  leading: const Icon(Icons.people),
                  children: profiles.entries.map((entry) {
                    final profile = entry.value;
                    return ListTile(
                      title: Text(profile.baseInfo.name),
                      subtitle: Text(profile.baseInfo.personalityTraits),
                      trailing: IconButton(
                        icon: const Icon(Icons.info_outline),
                        onPressed: () => _showCharacterDetails(profile.characterId),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      );
    });
  }

  Widget _buildActionButtons() {
    return Obx(() {
      return Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: _enhancedController.isGenerating.value
                  ? null
                  : _generateWorldBuilding,
              child: const Text('生成世界观'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _enhancedController.isGenerating.value ||
                      _enhancedController.currentWorldBuilding.value == null
                  ? null
                  : _generateCharacterProfiles,
              child: const Text('生成角色档案'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _enhancedController.isGenerating.value ||
                      _enhancedController.currentWorldBuilding.value == null ||
                      _enhancedController.currentCharacterProfiles.isEmpty
                  ? null
                  : _generateFullNovel,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
              ),
              child: const Text(
                '生成完整小说',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ],
      );
    });
  }

  void _generateWorldBuilding() async {
    if (!_validateInput()) return;

    try {
      await _enhancedController.generateWorldBuilding(
        novelTitle: _titleController.text,
        genres: _genreController.text.split(',').map((e) => e.trim()).toList(),
        theme: _themeController.text,
        targetReaders: _targetReadersController.text,
        totalChapters: int.parse(_chaptersController.text),
        characterCards: _novelController.getCompatibleCharacterCards(),
        background: _backgroundController.text.isEmpty ? null : _backgroundController.text,
      );

      Get.snackbar('成功', '世界观生成完成！');
    } catch (e) {
      Get.snackbar('错误', '生成世界观失败: $e');
    }
  }

  void _generateCharacterProfiles() async {
    final worldBuilding = _enhancedController.currentWorldBuilding.value;
    if (worldBuilding == null) return;

    try {
      await _enhancedController.generateCharacterProfiles(
        worldBuilding: worldBuilding,
        characterCards: _novelController.getCompatibleCharacterCards(),
      );

      Get.snackbar('成功', '角色档案生成完成！');
    } catch (e) {
      Get.snackbar('错误', '生成角色档案失败: $e');
    }
  }

  void _generateFullNovel() async {
    if (!_validateInput()) return;

    try {
      await _enhancedController.generateEnhancedNovel(
        novelTitle: _titleController.text,
        genres: _genreController.text.split(',').map((e) => e.trim()).toList(),
        theme: _themeController.text,
        targetReaders: _targetReadersController.text,
        totalChapters: int.parse(_chaptersController.text),
        characterCards: _novelController.getCompatibleCharacterCards(),
        background: _backgroundController.text.isEmpty ? null : _backgroundController.text,
        writingStyle: _novelController.selectedWritingStyle.value,
      );

      Get.snackbar('成功', '增强小说生成完成！');
    } catch (e) {
      Get.snackbar('错误', '生成小说失败: $e');
    }
  }

  bool _validateInput() {
    if (_titleController.text.isEmpty) {
      Get.snackbar('错误', '请输入小说标题');
      return false;
    }
    if (_genreController.text.isEmpty) {
      Get.snackbar('错误', '请输入小说类型');
      return false;
    }
    if (_themeController.text.isEmpty) {
      Get.snackbar('错误', '请输入小说主题');
      return false;
    }
    if (_targetReadersController.text.isEmpty) {
      Get.snackbar('错误', '请输入目标读者');
      return false;
    }
    if (_chaptersController.text.isEmpty || int.tryParse(_chaptersController.text) == null) {
      Get.snackbar('错误', '请输入有效的章节数');
      return false;
    }
    return true;
  }

  void _showCharacterDetails(String characterId) {
    final development = _enhancedController.getCharacterDevelopmentHistory(characterId);
    
    Get.dialog(
      AlertDialog(
        title: const Text('角色发展历程'),
        content: SingleChildScrollView(
          child: Text(development),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
