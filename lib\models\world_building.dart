import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'world_building.g.dart';

/// 世界观构建模型
@HiveType(typeId: 15)
class WorldBuilding {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String novelId;

  // 基础设定（根据章节数量调整详细程度）
  @HiveField(2)
  final String timeBackground; // 时代背景

  @HiveField(3)
  final String geographicalSetting; // 地理环境

  @HiveField(4)
  final String socialStructure; // 社会结构

  @HiveField(5)
  final String culturalBackground; // 文化背景

  // 规则体系（长篇小说才详细展开）
  @HiveField(6)
  final String? powerSystem; // 力量体系

  @HiveField(7)
  final String? economicSystem; // 经济体系

  @HiveField(8)
  final String? politicalSystem; // 政治体系

  @HiveField(9)
  final String? technologyLevel; // 科技水平

  // 特殊元素（可选）
  @HiveField(10)
  final String? specialItems; // 特殊物品/道具

  @HiveField(11)
  final String? importantLocations; // 重要地点

  @HiveField(12)
  final String? historicalEvents; // 历史事件

  @HiveField(13)
  final String? myths; // 神话传说

  @HiveField(14)
  final DateTime createdAt;

  @HiveField(15)
  final DateTime updatedAt;

  @HiveField(16)
  final WorldBuildingComplexity complexity; // 复杂度级别

  WorldBuilding({
    String? id,
    required this.novelId,
    required this.timeBackground,
    required this.geographicalSetting,
    required this.socialStructure,
    required this.culturalBackground,
    this.powerSystem,
    this.economicSystem,
    this.politicalSystem,
    this.technologyLevel,
    this.specialItems,
    this.importantLocations,
    this.historicalEvents,
    this.myths,
    DateTime? createdAt,
    DateTime? updatedAt,
    required this.complexity,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// 获取格式化的世界观描述
  String getFormattedDescription() {
    final buffer = StringBuffer();
    
    buffer.writeln('# 世界观设定\n');
    
    buffer.writeln('## 基础设定');
    buffer.writeln('**时代背景**: $timeBackground');
    buffer.writeln('**地理环境**: $geographicalSetting');
    buffer.writeln('**社会结构**: $socialStructure');
    buffer.writeln('**文化背景**: $culturalBackground\n');
    
    // 根据复杂度显示详细信息
    if (complexity != WorldBuildingComplexity.simple) {
      if (powerSystem?.isNotEmpty == true) {
        buffer.writeln('**力量体系**: $powerSystem');
      }
      if (economicSystem?.isNotEmpty == true) {
        buffer.writeln('**经济体系**: $economicSystem');
      }
      if (politicalSystem?.isNotEmpty == true) {
        buffer.writeln('**政治体系**: $politicalSystem');
      }
      if (technologyLevel?.isNotEmpty == true) {
        buffer.writeln('**科技水平**: $technologyLevel');
      }
    }
    
    if (complexity == WorldBuildingComplexity.complex) {
      buffer.writeln('\n## 特殊元素');
      if (specialItems?.isNotEmpty == true) {
        buffer.writeln('**特殊物品**: $specialItems');
      }
      if (importantLocations?.isNotEmpty == true) {
        buffer.writeln('**重要地点**: $importantLocations');
      }
      if (historicalEvents?.isNotEmpty == true) {
        buffer.writeln('**历史事件**: $historicalEvents');
      }
      if (myths?.isNotEmpty == true) {
        buffer.writeln('**神话传说**: $myths');
      }
    }
    
    return buffer.toString();
  }

  WorldBuilding copyWith({
    String? novelId,
    String? timeBackground,
    String? geographicalSetting,
    String? socialStructure,
    String? culturalBackground,
    String? powerSystem,
    String? economicSystem,
    String? politicalSystem,
    String? technologyLevel,
    String? specialItems,
    String? importantLocations,
    String? historicalEvents,
    String? myths,
    WorldBuildingComplexity? complexity,
  }) {
    return WorldBuilding(
      id: id,
      novelId: novelId ?? this.novelId,
      timeBackground: timeBackground ?? this.timeBackground,
      geographicalSetting: geographicalSetting ?? this.geographicalSetting,
      socialStructure: socialStructure ?? this.socialStructure,
      culturalBackground: culturalBackground ?? this.culturalBackground,
      powerSystem: powerSystem ?? this.powerSystem,
      economicSystem: economicSystem ?? this.economicSystem,
      politicalSystem: politicalSystem ?? this.politicalSystem,
      technologyLevel: technologyLevel ?? this.technologyLevel,
      specialItems: specialItems ?? this.specialItems,
      importantLocations: importantLocations ?? this.importantLocations,
      historicalEvents: historicalEvents ?? this.historicalEvents,
      myths: myths ?? this.myths,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      complexity: complexity ?? this.complexity,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'novelId': novelId,
    'timeBackground': timeBackground,
    'geographicalSetting': geographicalSetting,
    'socialStructure': socialStructure,
    'culturalBackground': culturalBackground,
    'powerSystem': powerSystem,
    'economicSystem': economicSystem,
    'politicalSystem': politicalSystem,
    'technologyLevel': technologyLevel,
    'specialItems': specialItems,
    'importantLocations': importantLocations,
    'historicalEvents': historicalEvents,
    'myths': myths,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'complexity': complexity.name,
  };

  factory WorldBuilding.fromJson(Map<String, dynamic> json) => WorldBuilding(
    id: json['id'] as String,
    novelId: json['novelId'] as String,
    timeBackground: json['timeBackground'] as String,
    geographicalSetting: json['geographicalSetting'] as String,
    socialStructure: json['socialStructure'] as String,
    culturalBackground: json['culturalBackground'] as String,
    powerSystem: json['powerSystem'] as String?,
    economicSystem: json['economicSystem'] as String?,
    politicalSystem: json['politicalSystem'] as String?,
    technologyLevel: json['technologyLevel'] as String?,
    specialItems: json['specialItems'] as String?,
    importantLocations: json['importantLocations'] as String?,
    historicalEvents: json['historicalEvents'] as String?,
    myths: json['myths'] as String?,
    createdAt: DateTime.parse(json['createdAt'] as String),
    updatedAt: DateTime.parse(json['updatedAt'] as String),
    complexity: WorldBuildingComplexity.values.firstWhere(
      (e) => e.name == json['complexity'],
      orElse: () => WorldBuildingComplexity.moderate,
    ),
  );
}

/// 世界观复杂度枚举
@HiveType(typeId: 16)
enum WorldBuildingComplexity {
  @HiveField(0)
  simple,
  @HiveField(1)
  moderate,
  @HiveField(2)
  complex;

  /// 根据章节数量确定复杂度
  static WorldBuildingComplexity fromChapterCount(int chapterCount) {
    if (chapterCount <= 5) {
      return WorldBuildingComplexity.simple;
    } else if (chapterCount <= 20) {
      return WorldBuildingComplexity.moderate;
    } else {
      return WorldBuildingComplexity.complex;
    }
  }

  /// 获取显示名称
  String get displayName {
    switch (this) {
      case WorldBuildingComplexity.simple:
        return '简单';
      case WorldBuildingComplexity.moderate:
        return '中等';
      case WorldBuildingComplexity.complex:
        return '复杂';
    }
  }

  /// 获取描述
  String get description {
    switch (this) {
      case WorldBuildingComplexity.simple:
        return '基础世界观设定，适合短篇小说';
      case WorldBuildingComplexity.moderate:
        return '中等复杂度世界观，适合中篇小说';
      case WorldBuildingComplexity.complex:
        return '复杂详细的世界观，适合长篇小说';
    }
  }
}
